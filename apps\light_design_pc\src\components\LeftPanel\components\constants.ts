import type { IRoomEntityType, IRoomEntityRealType } from "@layoutai/basic_data";

// 定义LeftPanel组件中的常量

export const listType = {
    WinDoor: "WinDoor",
    Structure: "Structure",
} as const;


const StructureMapping: {
    [key: string]: { type: IRoomEntityType; name: IRoomEntityRealType; alias: string };
} = {
    包管: { type: "StructureEntity", name: "Envelope_Pipe", alias: "Envelope_Pipe" },
    地台: { type: "StructureEntity", name: "Platform", alias: "Platform" },
    方柱: { type: "StructureEntity", name: "<PERSON>llar", alias: "<PERSON>llar" },
    横梁: { type: "StructureEntity", name: "<PERSON><PERSON>", alias: "Beam" },
    烟道: { type: "StructureEntity", name: "Flue", alias: "Flue" },
};
