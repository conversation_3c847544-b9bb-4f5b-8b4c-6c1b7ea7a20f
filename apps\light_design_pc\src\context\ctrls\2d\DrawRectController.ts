import { ControllerBase, Design2DContext } from "@layoutai/design_2d";
import type { PreviewRect2D } from "../../object/2d/PreviewRect2D";
import { appContext } from "../../AppContext";
import { Design2DStates } from "../../fsm/const/FSMConst";
import { DesignControllerType } from "../DesignControllerType";
import { Vector2 } from "three";
import { DesignEntityType } from "../../object/DesignEntityType";

/**
 * 矩形绘制控制器
 * 专门负责矩形的绘制操作
 */
export class DrawRectController extends ControllerBase {
  private _mouseDownPoint: { x: number, y: number } | null = null;
  private _previewRect: PreviewRect2D | null = null;
  private readonly _distanceThreshold = 5; // 距离阈值，超过此距离才创建预览

  constructor(context: Design2DContext) {
    super(DesignControllerType.DRAW_RECT_CTRL, context);
  }

  /**
   * 初始化控制器
   */
  public initialize(): void {
    this._reset();
  }

  /**
   * 控制器激活时的回调
   */
  public activate(): void {
    super.activate();
    this._reset();
  }

  /**
   * 控制器停用时的回调
   */
  public deactivate(): void {
    super.deactivate();
    this._reset();
  }

  /**
   * 处理鼠标按下事件
   */
  public onMouseDown (event: MouseEvent): void 
  {
    if(event.button !== 0) 
    {
      return;
    };
    
    // 将屏幕坐标转换为画布坐标
    const canvasPoint = this.screenToCanvas(new Vector2(event.clientX, event.clientY));
    
    // 第一次点击 - 设置第一个点
    this._mouseDownPoint = canvasPoint;
    
    // 创建预览矩形
    const previewUuid = `preview-rect-${Date.now()}`;
    this._previewRect = appContext.design2DContext?.object2DManager.createObject2D(DesignEntityType.previewRect2D as any, previewUuid) as PreviewRect2D;
    console.log(this._previewRect);
    if (this._previewRect) {
      this._previewRect.x = canvasPoint.x;
      this._previewRect.y = canvasPoint.y;
      this._previewRect.setSize(0, 0); // 初始大小为0
      this.context.canvas2DManager.updateCanvas(true);
    }
  }

  /**
   * 处理鼠标移动事件
   */
  public onMouseMove (event: MouseEvent): void 
  {
    if(!this._mouseDownPoint || !this._previewRect) return;
    
    // 将屏幕坐标转换为画布坐标
    const canvasPoint = this.screenToCanvas(new Vector2(event.clientX, event.clientY));
    
    // 计算矩形的宽度和高度
    const width = Math.abs(canvasPoint.x - this._mouseDownPoint.x);
    const height = Math.abs(canvasPoint.y - this._mouseDownPoint.y);
    
    // 计算矩形的左上角坐标
    const left = Math.min(canvasPoint.x, this._mouseDownPoint.x);
    const top = Math.min(canvasPoint.y, this._mouseDownPoint.y);
    
    // 更新预览矩形的位置和大小
    this._previewRect.x = left;
    this._previewRect.y = top;
    this._previewRect.setSize(width, height);
    
    // 更新画布
    appContext.design2DContext?.canvas2DManager.updateCanvas(true);
  }

  /**
   * 处理鼠标抬起事件
   */
  public onMouseUp (event: MouseEvent): void 
  {
    if(event.button === 2) 
    {
      appContext.mainFSM.transitionTo(Design2DStates.IDLE2D);
      return;
    };
    if(event.button !== 0) return;
    
    if(this._mouseDownPoint && this._previewRect) {
      // 将屏幕坐标转换为画布坐标
      const canvasPoint = this.screenToCanvas(new Vector2(event.clientX, event.clientY));
      
      // 计算矩形的宽度和高度
      const width = Math.abs(canvasPoint.x - this._mouseDownPoint.x);
      const height = Math.abs(canvasPoint.y - this._mouseDownPoint.y);
      
      // 如果矩形太小，则忽略
      if(width < this._distanceThreshold || height < this._distanceThreshold) {
        console.log('矩形太小，忽略');
        this._reset();
        return;
      }
      
      // 计算矩形的左上角坐标
      const left = Math.min(canvasPoint.x, this._mouseDownPoint.x);
      const top = Math.min(canvasPoint.y, this._mouseDownPoint.y);
      
      // 更新预览矩形的位置和大小
      this._previewRect.x = left;
      this._previewRect.y = top;
      this._previewRect.setSize(width, height);
      
      // 更新画布
      this.context.canvas2DManager.updateCanvas(true);
    }
    
    this._reset();
  }

  private _reset(): void {
    // 清理预览矩形
    if (this._previewRect) {
      // 从对象管理器中移除
      this.context.object2DManager.removeObject2D(this._previewRect.uuid);
      this._previewRect = null;
      // 触发画布重新渲染
      this.context.canvas2DManager.updateCanvas(true);
    }
    
    this._mouseDownPoint = null;
  }


  /**
   * 销毁控制器
   */
  public dispose(): void {
    this._reset();
    super.dispose();
  }
}