import { ControllerBase, Design2DContext } from "@layoutai/design_2d";
import type { Object2DBase } from "@layoutai/design_2d";
import { appContext } from "../../AppContext";
import { Design2DStates } from "../../fsm/const/FSMConst";
import { DesignControllerType } from "../DesignControllerType";
import { Vector3 } from "three";
import { DesignEntityType } from "../../object/DesignEntityType";
import { DomainApiHub } from "@layoutai/design_domain";
import { g_FigureImagePaths } from "@layoutai/design_2d";
import type { IRoomEntityType } from "@layoutai/basic_data";

/**
 * 门窗绘制控制器
 * 专门负责门窗的绘制操作
 */

const EntityType: { [key: string]: IRoomEntityType } = {
    WINDOW: "Window",
    DOOR: "Door",
};

const WinDoorMapping: {
    [key: string]: { type: IRoomEntityType };
} = {
    单开门: { type: EntityType.DOOR },
    推拉门: { type: EntityType.DOOR },
    一字窗: { type: EntityType.WINDOW },
    飘窗: { type: EntityType.WINDOW },
    双开门: { type: EntityType.DOOR },
    子母门: { type: EntityType.DOOR },
    门洞: { type: EntityType.DOOR },
    垭口: { type: EntityType.DOOR },
    栏杆: { type: EntityType.WINDOW },
};

export class DrawWinDoorController extends ControllerBase {
    private _mouseDownPoint: { x: number; y: number } | null = null;
    private _selectItem: any | null = null;
    private _object2D: Object2DBase | null = null;

    constructor(context: Design2DContext) {
        super(DesignControllerType.DRAW_WINDOOR_CTRL, context);
    }

    public initialize(): void {
        this._reset();
    }

    public activate(data: any): void {
        super.activate();
        this._reset();
        // 初次点击需要触发绘制
        if (data) {
            const { event, item } = data as { event: MouseEvent; item: any };
            this._selectItem = item;
            this.onMouseDown(event);
        }
    }

    public deactivate(): void {
        super.deactivate();
        this._reset();
    }

    // 获取世界坐标
    private _getGlobalPoint(event: MouseEvent): { x: number; y: number } {
        const canvasPoint = this.screenToCanvas(new Vector3(event.clientX, event.clientY));
        const globalPoint = this.canvasToGlobal(canvasPoint);
        return { ...globalPoint };
    }

    // 创建门窗实体
    private async _addWinDoorEntity(): Promise<string | null> {
        const title = this._selectItem.title;
        const realType = this._selectItem.label;
        const config = { ...g_FigureImagePaths[title], ...WinDoorMapping[title] };
        const pose = {
            x: this._mouseDownPoint?.x || 0,
            y: this._mouseDownPoint?.y || 0,
            // 默认法向
            normalX: 0,
            normalY: -1,
            normalZ: 0,
            length: config.length!,
            width: config.depth!,
        };
        let uuid: string = "";
        if (config && config.type == EntityType.WINDOW) {
            uuid = await DomainApiHub.instance.createWindow(config.type, realType, pose);
        } else if (config && config.type == EntityType.DOOR) {
            uuid = await DomainApiHub.instance.createDoor(config.type, realType, pose);
        }
        return uuid;
    }

    public async onMouseDown(event: MouseEvent): Promise<void> {
        if (event.button == 1) return;
        const title = this._selectItem.title;
        // 坐标转换
        this._mouseDownPoint = this._getGlobalPoint(event);
        // 创建实体
        const uuid = await this._addWinDoorEntity();
        if (uuid) {
            // 创建2D对象
            const entityType = WinDoorMapping[title].type === EntityType.WINDOW ? DesignEntityType.window : DesignEntityType.door;
            const object2D = appContext.design2DContext?.object2DManager.createObject2D(entityType, uuid);
            if (object2D) {
                this._object2D = object2D;
                console.log(this._object2D);
                this.context.canvas2DManager.updateCanvas(true);
            }
        }
    }

    public onMouseMove(event: MouseEvent): void {
        if (!this._mouseDownPoint || !this._object2D) return;
        // 更新坐标
        this._mouseDownPoint = this._getGlobalPoint(event);
        // 更新2D对象
        this.context.object2DManager.updateObject2D
        // 更新画布
        // appContext.design2DContext?.canvas2DManager.updateCanvas(true);
    }

    public onMouseUp(event: MouseEvent): void {}

    private _reset(): void {
        if (this._object2D) {
            this.context.object2DManager.removeObject2D(this._object2D.uuid);
            this._object2D = null;
            this.context.canvas2DManager.updateCanvas(true);
        }
        this._mouseDownPoint = null;
    }

    public dispose(): void {
        this._reset();
        super.dispose();
    }
}
